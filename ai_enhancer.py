#!/usr/bin/env python3
"""
AI Text Enhancer for Vietnamese Chapter Content

This module provides AI-powered text enhancement using Google Gemini AI
to improve Vietnamese writing style and quality according to format.md guidelines.
"""

import os
import time
import logging
from typing import Optional, Dict, Any
from pathlib import Path
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class GeminiTextEnhancer:
    """
    AI-powered text enhancer using Google Gemini for Vietnamese content improvement.
    """
    
    def __init__(self, api_key: Optional[str] = None, model_name: str = "gemini-2.5-flash"):
        """
        Initialize the Gemini AI enhancer.
        
        Args:
            api_key: Google AI Studio API key (if None, will try to get from environment)
            model_name: Gemini model to use for text enhancement
        """
        self.api_key = api_key or os.getenv('GOOGLE_AI_API_KEY')
        self.model_name = model_name
        self.model = None
        self.rate_limit_delay = 1.0  # Delay between API calls in seconds
        self.max_retries = 3
        
        # Load formatting guidelines
        self.format_guidelines = self._load_format_guidelines()
        
        if not self.api_key:
            raise ValueError(
                "Google AI API key not found. Please set GOOGLE_AI_API_KEY environment variable "
                "or pass it directly to the constructor."
            )
        
        self._initialize_model()
    
    def _load_format_guidelines(self) -> str:
        """Load the format.md guidelines for AI processing."""
        format_file = Path("format.md")
        if format_file.exists():
            try:
                with open(format_file, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception as e:
                logging.warning(f"Could not load format.md: {e}")
                return ""
        return ""
    
    def _initialize_model(self) -> None:
        """Initialize the Gemini AI model."""
        try:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel(self.model_name)
            logging.info(f"Initialized Gemini model: {self.model_name}")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Gemini model: {e}")
    
    def _create_enhancement_prompt(self, chapter_name: str, chapter_content: str) -> str:
        """
        Create a detailed prompt for AI text enhancement.
        
        Args:
            chapter_name: The chapter title
            chapter_content: The chapter content to enhance
            
        Returns:
            A comprehensive prompt for AI processing
        """
        prompt = f"""
Bạn là một chuyên gia chỉnh sửa văn bản tiếng Việt chuyên về thể loại truyện tu tiên/cổ đại. 
Nhiệm vụ của bạn là cải thiện chất lượng văn phong của đoạn văn bản sau đây theo các hướng dẫn cụ thể.

## HƯỚNG DẪN CHỈNH SỬA:

{self.format_guidelines}

## NGUYÊN TẮC QUAN TRỌNG:
1. **KHÔNG THAY ĐỔI NỘI DUNG**: Chỉ cải thiện văn phong, không thêm bớt tình tiết
2. **GIỮ NGUYÊN TÊN RIÊNG**: Tất cả tên nhân vật, địa danh, công pháp phải giữ nguyên
3. **NHÂN XƯNG NHẤT QUÁN**: Sử dụng đúng cách xưng hô theo vai vế và mối quan hệ
4. **VĂN PHONG CỔ ĐIỂN**: Dùng từ ngữ Hán-Việt, tránh từ ngữ hiện đại
5. **GIỮ NGUYÊN CẤU TRÚC**: Không thay đổi số đoạn văn và cấu trúc chung

## CHƯƠNG CẦN CHỈNH SỬA:

**Tiêu đề:** {chapter_name}

**Nội dung:**
{chapter_content}

## YÊU CẦU XUẤT:
Hãy trả về CHỈ nội dung đã được chỉnh sửa, không bao gồm tiêu đề hay bất kỳ giải thích nào khác. 
Nội dung phải được cải thiện về mặt văn phong nhưng giữ nguyên ý nghĩa và cấu trúc gốc.
"""
        return prompt.strip()
    
    def enhance_text(self, chapter_name: str, chapter_content: str) -> Optional[str]:
        """
        Enhance Vietnamese text using Gemini AI.
        
        Args:
            chapter_name: The chapter title
            chapter_content: The chapter content to enhance
            
        Returns:
            Enhanced text or None if enhancement fails
        """
        if not self.model:
            logging.error("Gemini model not initialized")
            return None
        
        if not chapter_content.strip():
            logging.warning("Empty chapter content provided")
            return chapter_content
        
        prompt = self._create_enhancement_prompt(chapter_name, chapter_content)
        
        for attempt in range(self.max_retries):
            try:
                # Add delay to respect rate limits
                if attempt > 0:
                    time.sleep(self.rate_limit_delay * (2 ** attempt))  # Exponential backoff
                
                logging.info(f"Enhancing text for chapter: {chapter_name[:50]}... (attempt {attempt + 1})")
                
                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.3,  # Lower temperature for more consistent results
                        max_output_tokens=8192,  # Allow for longer responses
                        top_p=0.8,
                        top_k=40
                    )
                )
                
                if response and response.text:
                    enhanced_text = response.text.strip()
                    logging.info(f"Successfully enhanced chapter: {chapter_name[:50]}...")
                    return enhanced_text
                else:
                    logging.warning(f"Empty response from Gemini for chapter: {chapter_name[:50]}...")
                    
            except Exception as e:
                logging.error(f"Error enhancing text (attempt {attempt + 1}): {e}")
                if attempt == self.max_retries - 1:
                    logging.error(f"Failed to enhance chapter after {self.max_retries} attempts: {chapter_name}")
                    return None
                
                # Wait before retrying
                time.sleep(self.rate_limit_delay)
        
        return None
    
    def is_available(self) -> bool:
        """
        Check if the AI enhancer is available and properly configured.
        
        Returns:
            True if the enhancer is ready to use, False otherwise
        """
        return self.model is not None and self.api_key is not None
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of the AI enhancer.
        
        Returns:
            Dictionary containing status information
        """
        return {
            "model_name": self.model_name,
            "api_key_configured": bool(self.api_key),
            "model_initialized": self.model is not None,
            "format_guidelines_loaded": bool(self.format_guidelines),
            "rate_limit_delay": self.rate_limit_delay,
            "max_retries": self.max_retries
        }


def test_enhancer():
    """Test function for the AI enhancer."""
    try:
        enhancer = GeminiTextEnhancer()
        
        if not enhancer.is_available():
            print("❌ AI Enhancer not available. Check your API key configuration.")
            return
        
        print("✅ AI Enhancer initialized successfully")
        print("Status:", enhancer.get_status())
        
        # Test with sample text
        test_chapter = "Test Chapter"
        test_content = """Lâm Bạch không biết Cố Thanh Hàn có bao nhiêu cảm động, hắn chỉ cảm thấy Tô Mị người thị nữ này thật sự là quá không hiểu chuyện! Sáng sớm, lại một lần bị liên lụy đến vết thương về sau, Lâm Bạch kêu dừng Tô Mị vì hắn cởi áo hành vi."""
        
        print(f"\n🔄 Testing enhancement...")
        enhanced = enhancer.enhance_text(test_chapter, test_content)
        
        if enhanced:
            print("✅ Enhancement successful!")
            print(f"Original: {test_content[:100]}...")
            print(f"Enhanced: {enhanced[:100]}...")
        else:
            print("❌ Enhancement failed")
            
    except Exception as e:
        print(f"❌ Error testing enhancer: {e}")


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    test_enhancer()

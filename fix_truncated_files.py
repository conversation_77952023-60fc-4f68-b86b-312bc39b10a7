#!/usr/bin/env python3
"""
Script to fix truncated enhanced files by regenerating them
"""

import os
import sys
from pathlib import Path
from text_enhancer import TextEnhancer
import logging

def identify_truncated_files():
    """Identify files that need to be regenerated due to truncation."""
    original_dir = Path('output')
    enhanced_dir = Path('enhanced_output')
    
    truncated_files = []
    
    for orig_file in original_dir.glob('*.md'):
        enhanced_file = enhanced_dir / (orig_file.stem + '_enhanced.md')
        
        if not enhanced_file.exists():
            continue
        
        try:
            with open(orig_file, 'r', encoding='utf-8') as f:
                orig_content = f.read()
            
            with open(enhanced_file, 'r', encoding='utf-8') as f:
                enhanced_content = f.read()
            
            orig_size = len(orig_content)
            enhanced_size = len(enhanced_content)
            ratio = enhanced_size / orig_size if orig_size > 0 else 0
            
            # Check for significant content loss (less than 70% of original)
            if ratio < 0.7:
                truncated_files.append({
                    'original': orig_file,
                    'enhanced': enhanced_file,
                    'ratio': ratio,
                    'orig_size': orig_size,
                    'enhanced_size': enhanced_size
                })
                
        except Exception as e:
            print(f"Error analyzing {orig_file.name}: {e}")
    
    return truncated_files

def fix_truncated_files(truncated_files, force_regenerate=True):
    """Fix truncated files by regenerating them."""
    if not truncated_files:
        print("No truncated files found!")
        return
    
    print(f"Found {len(truncated_files)} truncated files to fix:")
    for file_info in truncated_files:
        print(f"  - {file_info['original'].name} ({file_info['ratio']:.1%} of original)")
    
    print("\nRegenerating truncated files...")
    
    # Create a temporary directory for regenerated files
    temp_dir = Path('temp_regenerated')
    temp_dir.mkdir(exist_ok=True)
    
    # Initialize enhancer
    enhancer = TextEnhancer(
        input_path="dummy",  # Will be overridden
        output_dir=str(temp_dir),
        ai_enabled=True,
        preserve_structure=False,
        suffix="_fixed"
    )
    
    success_count = 0
    
    for i, file_info in enumerate(truncated_files, 1):
        orig_file = file_info['original']
        enhanced_file = file_info['enhanced']
        
        print(f"\n[{i}/{len(truncated_files)}] Processing: {orig_file.name}")
        
        try:
            # Process the file
            result = enhancer.process_file(orig_file)
            
            if result.success and result.enhanced:
                # Move the regenerated file to replace the truncated one
                temp_file = temp_dir / (orig_file.stem + "_fixed.md")
                if temp_file.exists():
                    # Backup the old truncated file
                    backup_file = enhanced_file.with_suffix('.md.backup')
                    enhanced_file.rename(backup_file)
                    
                    # Replace with the fixed version
                    temp_file.rename(enhanced_file)
                    
                    print(f"✅ Fixed: {orig_file.name}")
                    success_count += 1
                else:
                    print(f"❌ Failed: No output file generated for {orig_file.name}")
            else:
                print(f"❌ Failed: {result.error_message or 'Unknown error'}")
                
        except Exception as e:
            print(f"❌ Error processing {orig_file.name}: {e}")
    
    # Clean up temp directory
    try:
        temp_dir.rmdir()
    except:
        pass
    
    print(f"\n🎉 Successfully fixed {success_count}/{len(truncated_files)} files!")
    
    if success_count < len(truncated_files):
        print(f"⚠️  {len(truncated_files) - success_count} files still need attention.")

def main():
    """Main function."""
    print("🔍 Analyzing files for truncation issues...")
    
    truncated_files = identify_truncated_files()
    
    if not truncated_files:
        print("✅ No truncated files found!")
        return
    
    print(f"\n⚠️  Found {len(truncated_files)} truncated files:")
    for file_info in truncated_files:
        print(f"  - {file_info['original'].name}: {file_info['orig_size']} -> {file_info['enhanced_size']} chars ({file_info['ratio']:.1%})")
    
    response = input(f"\nDo you want to regenerate these {len(truncated_files)} files? (y/N): ")
    if response.lower() in ['y', 'yes']:
        fix_truncated_files(truncated_files)
    else:
        print("Skipping regeneration.")

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    main()
